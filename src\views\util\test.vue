<template>
  <basic-container>
    <div class="calendar-panel-demo">
      <h2>日历面板组件演示</h2>
      <div class="demo-section">
        <h3>选中的日期范围：</h3>
        <p v-if="selectedRange.start && selectedRange.end">
          {{ selectedRange.start }} 至 {{ selectedRange.end }}
        </p>
        <p v-else>请选择日期范围</p>
      </div>

      <!-- 日历面板组件 -->
      <CalendarPanel v-model="selectedRange" />
    </div>
  </basic-container>
</template>

<script setup>
import CalendarPanel from './components/CalendarPanel.vue';

// 选中的日期范围
const selectedRange = ref({
  start: '',
  end: '',
});

// 监听日期范围变化
watch(
  selectedRange,
  newRange => {
    console.log('选中的日期范围:', newRange);
  },
  { deep: true },
);
</script>

<style scoped>
.calendar-panel-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.demo-section h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.demo-section p {
  margin: 0;
  font-size: 16px;
  color: #666;
}
</style>
